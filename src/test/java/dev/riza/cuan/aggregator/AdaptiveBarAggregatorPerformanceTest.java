package dev.riza.cuan.aggregator;

import dev.riza.cuan.config.IndicatorRegistry;
import dev.riza.cuan.domain.model.AdaptiveBar;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.MarketConditionEvent;
import dev.riza.cuan.indicator.MarketActivityMonitor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * Performance and correctness tests for the optimized AdaptiveBarAggregator
 */
class AdaptiveBarAggregatorPerformanceTest {

    @Mock
    private MarketActivityMonitor marketActivityMonitor;
    
    @Mock
    private IndicatorRegistry indicatorRegistry;

    private AdaptiveBarAggregator aggregator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        aggregator = new AdaptiveBarAggregator(
            new BigDecimal("100"), // baseVolumeThreshold
            50, // baseTradeCountThreshold
            new BigDecimal("0.001"), // baseVolatilityMultiplier
            marketActivityMonitor,
            indicatorRegistry
        );
    }

    @Test
    void testIncrementalCalculationsCorrectness() {
        // Create test market condition
        MarketConditionEvent marketCondition = MarketConditionEvent.builder()
            .timestamp(Instant.now())
            .rollingVolatility(new BigDecimal("0.0001"))
            .tradesPerSecond(5.0)
            .volumeRate(new BigDecimal("50"))
            .build();

        // Create test trades
        List<AggTradeEvent> trades = createTestTrades();
        
        // Process trades
        for (AggTradeEvent trade : trades) {
            Message<AggTradeEvent> message = MessageBuilder.withPayload(trade).build();
            aggregator.onMarketData(message, marketCondition);
        }

        // Verify that the aggregator processes trades without errors
        // In a real test, you would verify the output bar values
        assertNotNull(aggregator.getStream());
    }

    @Test
    void testPerformanceWithHighVolumeData() {
        // Create market condition that triggers adaptive behavior
        MarketConditionEvent marketCondition = MarketConditionEvent.builder()
            .timestamp(Instant.now())
            .rollingVolatility(new BigDecimal("0.001")) // High volatility
            .tradesPerSecond(15.0) // High TPS
            .volumeRate(new BigDecimal("200"))
            .build();

        // Create a large number of test trades
        List<AggTradeEvent> trades = createLargeTestDataset(1000);
        
        long startTime = System.nanoTime();
        
        // Process all trades
        for (AggTradeEvent trade : trades) {
            Message<AggTradeEvent> message = MessageBuilder.withPayload(trade).build();
            aggregator.onMarketData(message, marketCondition);
        }
        
        long endTime = System.nanoTime();
        long durationMs = (endTime - startTime) / 1_000_000;
        
        // Performance assertion - should process 1000 trades in reasonable time
        assertTrue(durationMs < 100, "Processing 1000 trades took too long: " + durationMs + "ms");
        
        System.out.println("Processed 1000 trades in " + durationMs + "ms");
    }

    @Test
    void testBarClosureWithVolumeThreshold() {
        MarketConditionEvent marketCondition = MarketConditionEvent.builder()
            .timestamp(Instant.now())
            .rollingVolatility(new BigDecimal("0.0001"))
            .tradesPerSecond(5.0)
            .volumeRate(new BigDecimal("50"))
            .build();

        // Create trades that will exceed volume threshold
        List<AggTradeEvent> trades = createTradesWithTotalVolume(new BigDecimal("150"));
        
        StepVerifier.create(aggregator.getStream())
            .then(() -> {
                for (AggTradeEvent trade : trades) {
                    Message<AggTradeEvent> message = MessageBuilder.withPayload(trade).build();
                    aggregator.onMarketData(message, marketCondition);
                }
            })
            .expectNextMatches(bar -> {
                assertNotNull(bar);
                assertTrue(bar.getTotalVolume().compareTo(new BigDecimal("100")) >= 0);
                return true;
            })
            .thenCancel()
            .verify(Duration.ofSeconds(5));
    }

    @Test
    void testOptimizedStringComparison() {
        // Test the optimized isBuyTrade method indirectly through trade processing
        MarketConditionEvent marketCondition = MarketConditionEvent.builder()
            .timestamp(Instant.now())
            .rollingVolatility(new BigDecimal("0.0001"))
            .tradesPerSecond(5.0)
            .volumeRate(new BigDecimal("50"))
            .build();

        // Create trades with different side formats
        AggTradeEvent buyTrade = createTrade("BTCUSDT", "50000", "1.0", "buy");
        AggTradeEvent sellTrade = createTrade("BTCUSDT", "50000", "1.0", "sell");
        AggTradeEvent buyTradeUpperCase = createTrade("BTCUSDT", "50000", "1.0", "BUY");
        
        // Process trades - should not throw exceptions
        assertDoesNotThrow(() -> {
            aggregator.onMarketData(MessageBuilder.withPayload(buyTrade).build(), marketCondition);
            aggregator.onMarketData(MessageBuilder.withPayload(sellTrade).build(), marketCondition);
            aggregator.onMarketData(MessageBuilder.withPayload(buyTradeUpperCase).build(), marketCondition);
        });
    }

    private List<AggTradeEvent> createTestTrades() {
        List<AggTradeEvent> trades = new ArrayList<>();
        Instant baseTime = Instant.now();
        
        for (int i = 0; i < 10; i++) {
            AggTradeEvent trade = new AggTradeEvent();
            trade.setSymbol("BTCUSDT");
            trade.setPrice(new BigDecimal("50000").add(new BigDecimal(i)));
            trade.setQuantity(new BigDecimal("0.1"));
            trade.setSide(i % 2 == 0 ? "buy" : "sell");
            trade.setTimestamp(baseTime.plusMillis(i * 100));
            trades.add(trade);
        }
        
        return trades;
    }

    private List<AggTradeEvent> createLargeTestDataset(int count) {
        List<AggTradeEvent> trades = new ArrayList<>();
        Instant baseTime = Instant.now();
        
        for (int i = 0; i < count; i++) {
            AggTradeEvent trade = new AggTradeEvent();
            trade.setSymbol("BTCUSDT");
            trade.setPrice(new BigDecimal("50000").add(new BigDecimal(i % 100)));
            trade.setQuantity(new BigDecimal("0.01"));
            trade.setSide(i % 2 == 0 ? "buy" : "sell");
            trade.setTimestamp(baseTime.plusMillis(i));
            trades.add(trade);
        }
        
        return trades;
    }

    private List<AggTradeEvent> createTradesWithTotalVolume(BigDecimal targetVolume) {
        List<AggTradeEvent> trades = new ArrayList<>();
        Instant baseTime = Instant.now();
        BigDecimal currentVolume = BigDecimal.ZERO;
        int i = 0;
        
        while (currentVolume.compareTo(targetVolume) < 0) {
            BigDecimal quantity = new BigDecimal("10");
            AggTradeEvent trade = createTrade("BTCUSDT", "50000", quantity.toString(), i % 2 == 0 ? "buy" : "sell");
            trade.setTimestamp(baseTime.plusMillis(i * 10));
            trades.add(trade);
            currentVolume = currentVolume.add(quantity);
            i++;
        }
        
        return trades;
    }

    private AggTradeEvent createTrade(String symbol, String price, String quantity, String side) {
        AggTradeEvent trade = new AggTradeEvent();
        trade.setSymbol(symbol);
        trade.setPrice(new BigDecimal(price));
        trade.setQuantity(new BigDecimal(quantity));
        trade.setSide(side);
        trade.setTimestamp(Instant.now());
        return trade;
    }
}
