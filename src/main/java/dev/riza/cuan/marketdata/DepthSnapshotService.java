package dev.riza.cuan.marketdata;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.riza.cuan.domain.model.OrderBookSnapshot;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Service
public class DepthSnapshotService {

    private final WebClient webClient = WebClient.create("https://fapi.binance.com");
    private final ObjectMapper mapper = new ObjectMapper();

    public OrderBookSnapshot fetchSnapshot(String symbol, int limit) {
        JsonNode response = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("/fapi/v1/depth")
                        .queryParam("symbol", symbol)
                        .queryParam("limit", limit)
                        .build())
                .retrieve()
                .bodyToMono(JsonNode.class)
                .block();

        return parseSnapshot(response);
    }

    private OrderBookSnapshot parseSnapshot(JsonNode json) {
        OrderBookSnapshot snapshot = new OrderBookSnapshot();
        snapshot.setLastUpdateId(json.get("lastUpdateId").asLong()); // <-- this line is missing
        snapshot.setBids(parseLevels(json.get("bids")));
        snapshot.setAsks(parseLevels(json.get("asks")));
        return snapshot;
    }

    private List<OrderBookSnapshot.Level> parseLevels(JsonNode array) {
        return StreamSupport.stream(array.spliterator(), false)
                .map(entry -> {
                    OrderBookSnapshot.Level level = new OrderBookSnapshot.Level();
                    level.setPrice(new BigDecimal(entry.get(0).asText()));
                    level.setQuantity(new BigDecimal(entry.get(1).asText()));
                    return level;
                })
                .collect(Collectors.toList());
    }
}
