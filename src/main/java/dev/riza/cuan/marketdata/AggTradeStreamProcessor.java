package dev.riza.cuan.marketdata;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.riza.cuan.config.MarketDataGateway;
import dev.riza.cuan.domain.model.AggTradeEvent;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;

@Component
public class AggTradeStreamProcessor extends WebSocketListener {

    private final MarketDataGateway marketDataGateway;

    public AggTradeStreamProcessor(MarketDataGateway marketDataGateway) {
        this.marketDataGateway = marketDataGateway;
    }

    @Override
    public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
        System.out.println("Connected to Binance aggTrade stream.");
    }

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
        try {
            // Parse the message
            JsonNode jsonNode = objectMapper.readTree(text);

            // Check if this is a combined stream message
            if (jsonNode.has("data")) {
                jsonNode = objectMapper.readTree(jsonNode.get("data").asText());
            }

            // Make sure this is an aggregate trade event
            if (!jsonNode.has("e") || !jsonNode.get("e").asText().equals("aggTrade")) {
                System.out.println("Not an aggTrade event: " + text);
                return;
            }

            // Extract the relevant fields
            String symbol = jsonNode.get("s").asText();
            BigDecimal price = new BigDecimal(jsonNode.get("p").asText());
            BigDecimal quantity = new BigDecimal(jsonNode.get("q").asText());
            boolean isBuyerMaker = jsonNode.get("m").asBoolean();
            String side = isBuyerMaker ? "sell" : "buy"; // If buyer is maker, then the aggressive side was sell
            long timestamp = jsonNode.get("E").asLong(); // Event time in milliseconds

            // Create the AggTrade object
            AggTradeEvent aggTrade = new AggTradeEvent();
            aggTrade.setSymbol(symbol);
            aggTrade.setPrice(price);
            aggTrade.setQuantity(quantity);
            aggTrade.setSide(side);
            aggTrade.setTimestamp(Instant.ofEpochMilli(timestamp));

            // Process the AggTrade (e.g., publish to a message channel or store in repository)
            processAggTrade(aggTrade);

        } catch (Exception e) {
            System.err.println("Error processing aggTrade message: " + e.getMessage());
            System.err.println("Raw message: " + text);
            e.printStackTrace();
        }
    }

    private void processAggTrade(AggTradeEvent aggTrade) {
        // TODO: Implement this method to publish the AggTrade to a message channel
        // or perform other processing as needed

        marketDataGateway.publish(aggTrade);
    }

    @Override
    public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, @Nullable Response response) {
        System.err.println("AggTrade stream failed: " + t.getMessage());
    }
}
