package dev.riza.cuan.marketdata;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.riza.cuan.config.MarketDataGateway;
import dev.riza.cuan.domain.model.DepthUpdateEvent;
import dev.riza.cuan.domain.model.OrderBookSnapshot;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

@Component
public class DepthStreamProcessor extends WebSocketListener {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final OrderBookManager orderBookManager;
    private final DepthSnapshotService snapshotService;
    private final String symbol = "BTCUSDT";
    private final int snapshotLimit = 1000;

    private boolean snapshotInitialized = false;
    private long snapshotLastUpdateId;
    private final Queue<DepthUpdateEvent> eventBuffer = new ConcurrentLinkedQueue<>();
    private final MarketDataGateway marketDataGateway;

    public DepthStreamProcessor(OrderBookManager orderBookManager, DepthSnapshotService snapshotService, MarketDataGateway marketDataGateway) {
        this.orderBookManager = orderBookManager;
        this.snapshotService = snapshotService;
        this.marketDataGateway = marketDataGateway;
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {
        System.out.println("[DepthStream] Connected to Binance depth stream");
        initializeOrderBook();
    }

    private void initializeOrderBook() {
        // Fetch initial snapshot
        try {
            OrderBookSnapshot snapshot = snapshotService.fetchSnapshot(symbol, snapshotLimit);
            snapshotLastUpdateId = snapshot.getLastUpdateId();
            
            // Initialize order book with snapshot
            orderBookManager.initializeFromSnapshot(snapshot);
            snapshotInitialized = true;
            
            System.out.println("[DepthStream] Initialized with snapshot. lastUpdateId = " + snapshotLastUpdateId);
        } catch (Exception e) {
            System.err.println("[DepthStream] Failed to initialize order book: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onMessage(WebSocket webSocket, String text) {
        try {
            // Parse the message
            JsonNode jsonNode = objectMapper.readTree(text);
            // Check if this is a combined stream message
            if (jsonNode.has("data")) {
                text = jsonNode.get("data").toString();
                jsonNode = objectMapper.readTree(text);
            }
            
            // Validate that this is a depth update event with required fields
            if (!isValidDepthUpdateEvent(jsonNode)) {
                System.err.println("[DepthStream] Invalid depth update event received: " + text);
                return;
            }

            DepthUpdateEvent event = objectMapper.readValue(text, DepthUpdateEvent.class);

            // Process the event
            if (!snapshotInitialized) {
                // If snapshot not initialized, buffer event and initialize
                eventBuffer.add(event);
                initializeOrderBook();
                processBufferedEvents();
            } else {
                processEvent(event);
            }
        } catch (Exception e) {
            System.err.println("[DepthStream] Error processing message: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void processEvent(DepthUpdateEvent event) {
        // Drop any event where u is < lastUpdateId in the snapshot
        if (event.getFinalUpdateId() < snapshotLastUpdateId) {
            System.out.println("[DepthStream] Dropping old event: u=" + event.getFinalUpdateId() + 
                              " < lastUpdateId=" + snapshotLastUpdateId);
            return;
        }

        // Apply the update
        boolean success = orderBookManager.applyDelta(event);
        
        if (!success) {
            System.out.println("[DepthStream] Event processing failed, reinitializing order book...");
            resetAndReinitialize();
            eventBuffer.add(event); // Save event for reprocessing
        } else {

            OrderBookSnapshot orderBookSnapshot = orderBookManager.generateSnapshot();
//            marketDataGateway.publish(orderBookSnapshot);
        }
    }

    private void processBufferedEvents() {
        if (!snapshotInitialized) {
            System.out.println("[DepthStream] Cannot process buffered events, snapshot not initialized");
            return;
        }

        System.out.println("[DepthStream] Processing " + eventBuffer.size() + " buffered events");
        
        // Create a new queue for events we couldn't process
        Queue<DepthUpdateEvent> remainingEvents = new ConcurrentLinkedQueue<>();
        
        while (!eventBuffer.isEmpty()) {
            DepthUpdateEvent event = eventBuffer.poll();
            
            // Drop events older than the snapshot
            if (event.getFinalUpdateId() < snapshotLastUpdateId) {
                System.out.println("[DepthStream] Dropping buffered event: u=" + event.getFinalUpdateId() + 
                                  " < lastUpdateId=" + snapshotLastUpdateId);
                continue;
            }
            
            // Try to apply the event
            boolean success = orderBookManager.applyDelta(event);
            
            if (!success) {
                // If we couldn't apply this event, add it and all remaining events to the remainingEvents queue
                System.out.println("[DepthStream] Failed to apply buffered event, reinitializing...");
                remainingEvents.add(event);
                remainingEvents.addAll(eventBuffer);
                eventBuffer.clear();
                resetAndReinitialize();
                eventBuffer.addAll(remainingEvents);
                return;
            }
        }
        
        System.out.println("[DepthStream] Finished processing buffered events. Book is now synchronized.");
    }

    private void resetAndReinitialize() {
        // Reset state
        snapshotInitialized = false;
        orderBookManager.reset();
        
        // Try to get a new snapshot
        initializeOrderBook();
    }

    /**
     * Validates that a JsonNode contains all the required fields for a depth update event
     * @param jsonNode The node to validate
     * @return true if the node is a valid depth update event, false otherwise
     */
    private boolean isValidDepthUpdateEvent(JsonNode jsonNode) {
        try {
            // Check for required fields in depth update events
            return jsonNode != null
                && jsonNode.has("e") && jsonNode.get("e").asText().equals("depthUpdate")
                && jsonNode.has("s") && jsonNode.get("s").asText().equals(symbol)
                && jsonNode.has("U") && jsonNode.has("u") // First and final update IDs
                && jsonNode.has("b") && jsonNode.has("a"); // Bids and asks
        } catch (Exception e) {
            System.err.println("[DepthStream] Error validating depth update event: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
        System.err.println("[DepthStream] WebSocket failure: " + t.getMessage());
    }

    @Override
    public void onClosed(WebSocket webSocket, int code, String reason) {
        System.out.println("[DepthStream] WebSocket closed: " + reason);
    }
}
