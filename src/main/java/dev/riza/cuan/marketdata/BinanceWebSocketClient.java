package dev.riza.cuan.marketdata;

import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

@Component
public class BinanceWebSocketClient {

    private final WebSocketConnectionManager connectionManager;
    private final DepthStreamProcessor depthStreamProcessor;
    private final AggTradeStreamProcessor aggTradeStreamProcessor;
    private final String symbol = "BTCUSDT";

    public BinanceWebSocketClient(WebSocketConnectionManager connectionManager,
                                  DepthStreamProcessor depthStreamProcessor,
                                  AggTradeStreamProcessor aggTradeStreamProcessor) {
        this.connectionManager = connectionManager;
        this.depthStreamProcessor = depthStreamProcessor;
        this.aggTradeStreamProcessor = aggTradeStreamProcessor;
    }

    @PostConstruct
    public void init() {
        System.out.println("Initializing Binance WebSocket Client for " + symbol);
        connectionManager.connect("depth", "wss://fstream.binance.com/stream?streams=btcusdt@depth", depthStreamProcessor);
        connectionManager.connect("aggTrade", "wss://fstream.binance.com/ws/btcusdt@aggTrade", aggTradeStreamProcessor);
    }
}
