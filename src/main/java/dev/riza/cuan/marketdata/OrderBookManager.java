package dev.riza.cuan.marketdata;

import dev.riza.cuan.domain.model.OrderBookSnapshot;
import dev.riza.cuan.domain.model.DepthUpdateEvent;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Component
public class OrderBookManager {
    // Default max depth for order book (per side)
    private static final int DEFAULT_DEPTH_LIMIT = 1000;
    
    // Thread-safe lock for order book updates to ensure consistency
    private final ReadWriteLock bookLock = new ReentrantReadWriteLock();
    
    // Order book price levels - using ConcurrentSkipListMap for thread safety and sorted order
    private final ConcurrentSkipListMap<BigDecimal, BigDecimal> bids = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
    private final ConcurrentSkipListMap<BigDecimal, BigDecimal> asks = new ConcurrentSkipListMap<>();

    // Order book state tracking
    private long lastUpdateId = -1;
    private boolean firstEventProcessed = false;
    
    // Configuration
    private int depthLimit = DEFAULT_DEPTH_LIMIT;
    
    // Performance metrics
    @Getter
    private final Map<String, AtomicLong> metrics = new ConcurrentHashMap<>();
    private Instant lastUpdateTime = Instant.now();
    private long updateCount = 0;
    private long successfulUpdates = 0;
    private long failedUpdates = 0;

    /**
     * Initialize the order book from a snapshot.
     *
     * @param snapshot The order book snapshot to initialize from
     */
    public void initializeFromSnapshot(OrderBookSnapshot snapshot) {
        bookLock.writeLock().lock();
        try {
            long startTime = System.nanoTime();
            
            bids.clear();
            asks.clear();
            snapshot.getBids().forEach(level -> bids.put(level.getPrice(), level.getQuantity()));
            snapshot.getAsks().forEach(level -> asks.put(level.getPrice(), level.getQuantity()));
            this.lastUpdateId = snapshot.getLastUpdateId();
            this.firstEventProcessed = false;
            
            // Enforce depth limits
            trimOrderBook();
            
            long endTime = System.nanoTime();
            getOrCreateMetric("snapshotInitTimeNanos").set(endTime - startTime);
            getOrCreateMetric("bidLevelsCount").set(bids.size());
            getOrCreateMetric("askLevelsCount").set(asks.size());
            
            System.out.println("Initialized order book from snapshot. lastUpdateId=" + lastUpdateId + 
                    " bidLevels=" + bids.size() + 
                    " askLevels=" + asks.size() + 
                    " initTimeMs=" + (endTime - startTime) / 1_000_000);
        } finally {
            bookLock.writeLock().unlock();
        }
    }

    /**
     * Apply an order book delta update.
     *
     * @param event The depth update event
     * @return true if the update was successfully applied, false otherwise
     */
    public boolean applyDelta(DepthUpdateEvent event) {
        updateCount++;
        long startTime = System.nanoTime();
        
        bookLock.writeLock().lock();
        try {
            long U = event.getFirstUpdateId();
            long u = event.getFinalUpdateId();
            long pu = event.getPreviousFinalUpdateId();

            // First event after snapshot needs special handling
            if (!firstEventProcessed) {
                // The first processed event should have U <= lastUpdateId AND u >= lastUpdateId
                if (U <= lastUpdateId && u >= lastUpdateId) {
                    getOrCreateMetric("firstEventLatencyNanos").set(System.nanoTime() - startTime);
                    
                    applyBookDeltas(bids, event.getBids());
                    applyBookDeltas(asks, event.getAsks());
                    this.lastUpdateId = u;
                    this.firstEventProcessed = true;
                    
                    // Enforce depth limits after update
                    trimOrderBook();
                    
                    successfulUpdates++;
                    return true;
                } else {
                    failedUpdates++;
                    System.err.println("First event condition not met. U=" + U + ", u=" + u + ", lastUpdateId=" + lastUpdateId);
                    return false;
                }
            } else {
                // For subsequent events, pu should equal the previous event's u (our lastUpdateId)
                if (pu != lastUpdateId) {
                    failedUpdates++;
                    System.err.println("Out-of-sync update received. Expected pu=" + lastUpdateId + ", got pu=" + pu);
                    return false;
                }

                applyBookDeltas(bids, event.getBids());
                applyBookDeltas(asks, event.getAsks());
                this.lastUpdateId = u;
                
                // Enforce depth limits after update
                trimOrderBook();
                
                // Track update frequency
                Instant now = Instant.now();
                long updateIntervalMs = now.toEpochMilli() - lastUpdateTime.toEpochMilli();
                getOrCreateMetric("lastUpdateIntervalMs").set(updateIntervalMs);
                lastUpdateTime = now;
                
                successfulUpdates++;
                return true;
            }
        } finally {
            long endTime = System.nanoTime();
            getOrCreateMetric("lastUpdateTimeNanos").set(endTime - startTime);
            getOrCreateMetric("updateCount").set(updateCount);
            getOrCreateMetric("successRate").set(successfulUpdates * 100 / Math.max(1, updateCount));
            
            // Update depth metrics
            getOrCreateMetric("bidLevelsCount").set(bids.size());
            getOrCreateMetric("askLevelsCount").set(asks.size());
            
            // Calculate and update spread
            BigDecimal bestBid = getBestBid();
            BigDecimal bestAsk = getBestAsk();
            if (bestBid != null && bestAsk != null) {
                BigDecimal spread = bestAsk.subtract(bestBid);
                getOrCreateMetric("spreadPoints").set(spread.movePointRight(5).longValue());
            }
            
            bookLock.writeLock().unlock();
        }
    }

    /**
     * Apply updates to a side of the order book.
     *
     * @param book    The order book side (bids or asks)
     * @param updates The updates to apply
     */
    private void applyBookDeltas(ConcurrentSkipListMap<BigDecimal, BigDecimal> book, List<List<String>> updates) {
        if (updates == null || updates.isEmpty()) {
            return;
        }
        
        for (List<String> entry : updates) {
            BigDecimal price = new BigDecimal(entry.get(0));
            BigDecimal qty = new BigDecimal(entry.get(1));
            if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                book.remove(price);
            } else {
                book.put(price, qty);
            }
        }
    }

    /**
     * Trim the order book to the configured depth limit.
     */
    private void trimOrderBook() {
        // Trim bids (highest prices first)
        while (bids.size() > depthLimit) {
            bids.pollLastEntry();
        }
        
        // Trim asks (lowest prices first)
        while (asks.size() > depthLimit) {
            asks.pollLastEntry();
        }
    }

    /**
     * Get a read-only snapshot of the current bid prices and quantities.
     *
     * @return A NavigableMap of bid prices to quantities
     */
    public NavigableMap<BigDecimal, BigDecimal> getBidLevels() {
        bookLock.readLock().lock();
        try {
            return bids.clone();
        } finally {
            bookLock.readLock().unlock();
        }
    }

    /**
     * Get a read-only snapshot of the current ask prices and quantities.
     *
     * @return A NavigableMap of ask prices to quantities
     */
    public NavigableMap<BigDecimal, BigDecimal> getAskLevels() {
        bookLock.readLock().lock();
        try {
            return asks.clone();
        } finally {
            bookLock.readLock().unlock();
        }
    }

    /**
     * Print the best prices for debugging.
     */
    public void printBestPrices() {
        bookLock.readLock().lock();
        try {
            System.out.println("Best Bid: " + getBestBid() + " | Best Ask: " + getBestAsk());
        } finally {
            bookLock.readLock().unlock();
        }
    }

    /**
     * Get the best bid price.
     *
     * @return The highest bid price or null if no bids exist
     */
    public BigDecimal getBestBid() {
        bookLock.readLock().lock();
        try {
            return bids.isEmpty() ? null : bids.firstKey();
        } finally {
            bookLock.readLock().unlock();
        }
    }

    /**
     * Get the best ask price.
     *
     * @return The lowest ask price or null if no asks exist
     */
    public BigDecimal getBestAsk() {
        bookLock.readLock().lock();
        try {
            return asks.isEmpty() ? null : asks.firstKey();
        } finally {
            bookLock.readLock().unlock();
        }
    }

    /**
     * Get the volume at a specific price level.
     *
     * @param price The price level
     * @param isBid Whether to check the bid or ask side
     * @return The volume at the price level, or zero if no orders exist at that price
     */
    public BigDecimal getVolumeAtPrice(BigDecimal price, boolean isBid) {
        bookLock.readLock().lock();
        try {
            ConcurrentSkipListMap<BigDecimal, BigDecimal> book = isBid ? bids : asks;
            return book.getOrDefault(price, BigDecimal.ZERO);
        } finally {
            bookLock.readLock().unlock();
        }
    }

    /**
     * Get the total volume up to a certain price level.
     *
     * @param price The price level
     * @param isBid Whether to check the bid or ask side
     * @return The cumulative volume up to the specified price
     */
    public BigDecimal getCumulativeVolumeToPrice(BigDecimal price, boolean isBid) {
        bookLock.readLock().lock();
        try {
            ConcurrentSkipListMap<BigDecimal, BigDecimal> book = isBid ? bids : asks;
            BigDecimal sum = BigDecimal.ZERO;
            
            if (isBid) {
                // For bids, include all prices >= the given price
                NavigableMap<BigDecimal, BigDecimal> subMap = book.tailMap(price, true);
                for (BigDecimal qty : subMap.values()) {
                    sum = sum.add(qty);
                }
            } else {
                // For asks, include all prices <= the given price
                NavigableMap<BigDecimal, BigDecimal> subMap = book.headMap(price, true);
                for (BigDecimal qty : subMap.values()) {
                    sum = sum.add(qty);
                }
            }
            
            return sum;
        } finally {
            bookLock.readLock().unlock();
        }
    }

    /**
     * Get the last update ID that was applied.
     *
     * @return The last update ID
     */
    public long getLastUpdateId() {
        return lastUpdateId;
    }
    
    /**
     * Reset the order book.
     */
    public void reset() {
        bookLock.writeLock().lock();
        try {
            bids.clear();
            asks.clear();
            lastUpdateId = -1;
            firstEventProcessed = false;
        } finally {
            bookLock.writeLock().unlock();
        }
    }
    
    /**
     * Check if the first event after a snapshot has been processed.
     *
     * @return true if the first event has been processed, false otherwise
     */
    public boolean isFirstEventProcessed() {
        return firstEventProcessed;
    }
    
    /**
     * Set the maximum number of price levels to maintain per side.
     *
     * @param limit The maximum number of levels to keep
     */
    public void setDepthLimit(int limit) {
        this.depthLimit = limit;
        trimOrderBook();
    }
    
    /**
     * Get the current depth limit.
     *
     * @return The depth limit
     */
    public int getDepthLimit() {
        return depthLimit;
    }
    
    /**
     * Get or create a metric counter.
     *
     * @param name The metric name
     * @return An AtomicLong counter for the metric
     */
    private AtomicLong getOrCreateMetric(String name) {
        return metrics.computeIfAbsent(name, k -> new AtomicLong(0));
    }
    
    /**
     * Generate an order book snapshot from the current state.
     *
     * @return An OrderBookSnapshot object
     */
    public OrderBookSnapshot generateSnapshot() {
        bookLock.readLock().lock();
        try {
            OrderBookSnapshot snapshot = new OrderBookSnapshot();
            snapshot.setLastUpdateId(lastUpdateId);
            
            List<OrderBookSnapshot.Level> bidLevels = bids.entrySet().stream()
                .map(entry -> {
                    OrderBookSnapshot.Level level = new OrderBookSnapshot.Level();
                    level.setPrice(entry.getKey());
                    level.setQuantity(entry.getValue());
                    return level;
                })
                .toList();
                
            List<OrderBookSnapshot.Level> askLevels = asks.entrySet().stream()
                .map(entry -> {
                    OrderBookSnapshot.Level level = new OrderBookSnapshot.Level();
                    level.setPrice(entry.getKey());
                    level.setQuantity(entry.getValue());
                    return level;
                })
                .toList();
                
            snapshot.setBids(bidLevels);
            snapshot.setAsks(askLevels);
            return snapshot;
        } finally {
            bookLock.readLock().unlock();
        }
    }
}
