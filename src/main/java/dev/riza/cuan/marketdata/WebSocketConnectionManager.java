package dev.riza.cuan.marketdata;

import okhttp3.*;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.*;

@Component
public class WebSocketConnectionManager {

    private final OkHttpClient client = new OkHttpClient.Builder()
            .readTimeout(0, TimeUnit.MILLISECONDS)
            .pingInterval(15, TimeUnit.SECONDS) // Helps keep connections alive
            .build();

    private final Map<String, WebSocket> activeSockets = new ConcurrentHashMap<>();
    private final Map<String, Instant> lastMessageTimestamps = new ConcurrentHashMap<>();
    private final ScheduledExecutorService monitorExecutor = Executors.newSingleThreadScheduledExecutor();

    private static final long MAX_IDLE_SECONDS = 30;

    public WebSocketConnectionManager() {
        startIdleMonitor();
    }

    public void connect(String streamName, String url, WebSocketListener listener) {
        Request request = new Request.Builder().url(url).build();
        WebSocket webSocket = client.newWebSocket(request, new ManagedWebSocketListener(streamName, url, listener));
        activeSockets.put(streamName, webSocket);
        lastMessageTimestamps.put(streamName, Instant.now());
    }

    public void disconnect(String streamName) {
        WebSocket socket = activeSockets.remove(streamName);
        lastMessageTimestamps.remove(streamName);
        if (socket != null) {
            socket.close(1000, "Normal closure");
        }
    }

    public void reconnect(String streamName, String url, WebSocketListener listener) {
        System.out.println("Reconnecting: " + streamName);
        disconnect(streamName);
        connect(streamName, url, listener);
    }

    private class ManagedWebSocketListener extends WebSocketListener {
        private final String streamName;
        private final String url;
        private final WebSocketListener delegate;

        ManagedWebSocketListener(String streamName, String url, WebSocketListener delegate) {
            this.streamName = streamName;
            this.url = url;
            this.delegate = delegate;
        }

        @Override
        public void onOpen(WebSocket webSocket, Response response) {
            delegate.onOpen(webSocket, response);
        }

        @Override
        public void onMessage(WebSocket webSocket, String text) {
            lastMessageTimestamps.put(streamName, Instant.now());
            delegate.onMessage(webSocket, text);
        }

        @Override
        public void onClosing(WebSocket webSocket, int code, String reason) {
            delegate.onClosing(webSocket, code, reason);
        }

        @Override
        public void onFailure(WebSocket webSocket, Throwable t, Response response) {
            System.err.println("Stream [" + streamName + "] failed: " + t.getMessage());
            reconnect(streamName, url, delegate); // Auto-reconnect on failure
        }
    }

    private void startIdleMonitor() {
        monitorExecutor.scheduleAtFixedRate(() -> {
            Instant now = Instant.now();
            for (Map.Entry<String, Instant> entry : lastMessageTimestamps.entrySet()) {
                String streamName = entry.getKey();
                Instant lastMessage = entry.getValue();

                if (Duration.between(lastMessage, now).getSeconds() > MAX_IDLE_SECONDS) {
                    System.err.println("Stream [" + streamName + "] is idle for too long. Reconnecting...");
                    WebSocket webSocket = activeSockets.get(streamName);
                    if (webSocket != null) {
                        webSocket.cancel(); // force close
                    }
                }
            }
        }, 10, 10, TimeUnit.SECONDS);
    }
}
