package dev.riza.cuan.config;

//import com.binance.connector.client.common.websocket.configuration.WebSocketClientConfiguration;
//import com.binance.connector.client.derivatives_trading_usds_futures.websocket.stream.DerivativesTradingUsdsFuturesWebSocketStreamsUtil;
//import com.binance.connector.client.derivatives_trading_usds_futures.websocket.stream.api.DerivativesTradingUsdsFuturesWebSocketStreams;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for Binance API clients
 */
@Configuration
public class BinanceConfig {
    
//    private static final Logger logger = LoggerFactory.getLogger(BinanceConfig.class);
//
//    /**
//     * Creates a WebSocketClientConfiguration bean for Binance WebSocket client
//     *
//     * @return WebSocketClientConfiguration configured for Binance
//     */
//    @Bean
//    public WebSocketClientConfiguration binanceWebSocketClientConfiguration() {
//        logger.info("Creating Binance WebSocketClientConfiguration");
//        WebSocketClientConfiguration clientConfiguration = DerivativesTradingUsdsFuturesWebSocketStreamsUtil.getClientConfiguration();
//
//        // Set any custom configuration if needed
//        // clientConfiguration.setProperty("idleTimeout", "120000");
//
//        return clientConfiguration;
//    }
//
//    /**
//     * Creates a SpotWebSocketStreams bean using the WebSocketClientConfiguration
//     *
//     * @param clientConfiguration the WebSocketClientConfiguration to use
//     * @return SpotWebSocketStreams client
//     */
//    @Bean
//    public DerivativesTradingUsdsFuturesWebSocketStreams binanceSpotWebSocketStreams(WebSocketClientConfiguration clientConfiguration) {
//        logger.info("Creating Binance SpotWebSocketStreams client");
//        return new DerivativesTradingUsdsFuturesWebSocketStreams(clientConfiguration);
//    }
}
