package dev.riza.cuan.config;

import org.springframework.integration.router.AbstractMessageRouter;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Component
public class IndicatorRouter extends AbstractMessageRouter {

    private final IndicatorRegistry indicatorRegistry;

    public IndicatorRouter(IndicatorRegistry indicatorRegistry) {
        this.indicatorRegistry = indicatorRegistry;
    }

    @Override
    protected Collection<MessageChannel> determineTargetChannels(Message<?> message) {
        List<MessageChannel> targets = new ArrayList<>();
        for (MarketDataIndicator indicator : indicatorRegistry.getActiveIndicators()) {
            targets.add(indicator.getInputChannel());
        }
        return targets;
    }
}
