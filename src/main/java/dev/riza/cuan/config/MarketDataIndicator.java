package dev.riza.cuan.config;

import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import reactor.core.publisher.Flux;

import java.time.Duration;

public interface MarketDataIndicator<I, O> {
    void onMarketData(Message<I> message); // e.g., AggTrade or DepthUpdateEvent
    MessageChannel getInputChannel();
    Duration getTimeframe();
    Flux<O> getStream();
}