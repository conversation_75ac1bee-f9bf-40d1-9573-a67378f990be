package dev.riza.cuan.domain.model;

import lombok.Builder;
import lombok.Data;
import java.math.BigDecimal;
import java.time.Instant;

@Data
@Builder
public class MarketConditionEvent {
    private Instant timestamp;
    private BigDecimal rollingVolatility; // e.g., std dev of price changes
    private double tradesPerSecond;
    private BigDecimal volumeRate; // e.g., total volume over last X seconds/trades
    // other metrics
}