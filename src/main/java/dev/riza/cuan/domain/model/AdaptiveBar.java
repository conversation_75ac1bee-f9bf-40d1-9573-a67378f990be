package dev.riza.cuan.domain.model;

import lombok.Builder;
import lombok.Data;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
@Builder
public class AdaptiveBar {
    private Instant openTime;
    private Instant closeTime;
    private String symbol;
    private BigDecimal open;
    private BigDecimal high;
    private BigDecimal low;
    private BigDecimal close;
    private BigDecimal vwap;
    private BigDecimal totalVolume;
    private BigDecimal buyVolume; // Volume dari buyer-initiated trades
    private BigDecimal sellVolume; // Volume dari seller-initiated trades
    private int tradeCount;
    private String closingReason; // e.g., "VOLATILITY_THRESHOLD", "VOLUME_THRESHOLD"

    // Opsional: sertakan snapshot kondisi pasar yang menyebabkan bar ini terbentuk
//    private MarketConditionEvent marketConditionSnapshot;
}