package dev.riza.cuan.util;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;

/**
 * Utility class for statistical calculations used in trading indicators.
 */
public class StatisticalUtils {
    
    private static final MathContext DEFAULT_MATH_CONTEXT = new MathContext(10, RoundingMode.HALF_UP);
    
    /**
     * Calculates the mean of a list of BigDecimal values.
     *
     * @param values The list of values
     * @param mc The MathContext to use for calculations
     * @return The mean value
     */
    public static BigDecimal mean(List<BigDecimal> values, MathContext mc) {
        if (values == null || values.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal sum = BigDecimal.ZERO;
        for (BigDecimal value : values) {
            sum = sum.add(value, mc);
        }
        
        return sum.divide(new BigDecimal(values.size()), mc);
    }
    
    /**
     * Calculates the standard deviation of a list of BigDecimal values.
     *
     * @param values The list of values
     * @param mc The MathContext to use for calculations
     * @return The standard deviation
     */
    public static BigDecimal standardDeviation(List<BigDecimal> values, MathContext mc) {
        if (values == null || values.size() < 2) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal mean = mean(values, mc);
        BigDecimal sumSquaredDiff = BigDecimal.ZERO;
        
        for (BigDecimal value : values) {
            BigDecimal diff = value.subtract(mean, mc);
            sumSquaredDiff = sumSquaredDiff.add(diff.multiply(diff, mc), mc);
        }
        
        BigDecimal variance = sumSquaredDiff.divide(new BigDecimal(values.size()), mc);
        return BigDecimal.valueOf(Math.sqrt(variance.doubleValue()));
    }
    
    /**
     * Calculates the exponentially weighted moving average (EWMA) of a list of BigDecimal values.
     *
     * @param values The list of values (in chronological order, oldest first)
     * @param alpha The smoothing factor (between 0 and 1)
     * @param mc The MathContext to use for calculations
     * @return The EWMA value
     */
    public static BigDecimal ewma(List<BigDecimal> values, double alpha, MathContext mc) {
        if (values == null || values.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal ewma = values.get(0);
        BigDecimal alphaBD = new BigDecimal(alpha);
        BigDecimal oneMinusAlpha = BigDecimal.ONE.subtract(alphaBD);
        
        for (int i = 1; i < values.size(); i++) {
            // EWMA = alpha * current + (1 - alpha) * previous_EWMA
            ewma = values.get(i).multiply(alphaBD, mc)
                    .add(ewma.multiply(oneMinusAlpha, mc), mc);
        }
        
        return ewma;
    }
    
    /**
     * Calculates the exponentially weighted standard deviation of a list of BigDecimal values.
     *
     * @param values The list of values (in chronological order, oldest first)
     * @param alpha The smoothing factor (between 0 and 1)
     * @param mc The MathContext to use for calculations
     * @return The exponentially weighted standard deviation
     */
    public static BigDecimal ewmStandardDeviation(List<BigDecimal> values, double alpha, MathContext mc) {
        if (values == null || values.size() < 2) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal ewma = values.get(0);
        BigDecimal ewmVar = BigDecimal.ZERO;
        BigDecimal alphaBD = new BigDecimal(alpha);
        BigDecimal oneMinusAlpha = BigDecimal.ONE.subtract(alphaBD);
        
        for (int i = 1; i < values.size(); i++) {
            BigDecimal current = values.get(i);
            // Update EWMA
            BigDecimal newEwma = current.multiply(alphaBD, mc)
                    .add(ewma.multiply(oneMinusAlpha, mc), mc);
            
            // Update exponentially weighted variance
            BigDecimal diff = current.subtract(ewma, mc);
            BigDecimal squaredDiff = diff.multiply(diff, mc);
            ewmVar = alphaBD.multiply(squaredDiff, mc)
                    .add(oneMinusAlpha.multiply(ewmVar, mc), mc);
            
            ewma = newEwma;
        }
        
        // Return the square root of the variance (standard deviation)
        return new BigDecimal(Math.sqrt(ewmVar.doubleValue()));
    }
    
    /**
     * Calculates the Average True Range (ATR) for a series of price data.
     * ATR is a measure of volatility that accounts for gaps in price movement.
     *
     * @param highs List of high prices
     * @param lows List of low prices
     * @param closes List of closing prices
     * @param period The period for ATR calculation
     * @param mc The MathContext to use for calculations
     * @return The ATR value
     */
    public static BigDecimal atr(List<BigDecimal> highs, List<BigDecimal> lows, 
                                List<BigDecimal> closes, int period, MathContext mc) {
        if (highs == null || lows == null || closes == null || 
            highs.size() < 2 || highs.size() != lows.size() || highs.size() != closes.size()) {
            return BigDecimal.ZERO;
        }
        
        int size = highs.size();
        BigDecimal[] trueRanges = new BigDecimal[size - 1];
        
        // Calculate true ranges
        for (int i = 1; i < size; i++) {
            BigDecimal highLowRange = highs.get(i).subtract(lows.get(i), mc);
            BigDecimal highCloseDiff = highs.get(i).subtract(closes.get(i - 1), mc).abs();
            BigDecimal lowCloseDiff = lows.get(i).subtract(closes.get(i - 1), mc).abs();
            
            // True Range is the greatest of the three
            BigDecimal tr = highLowRange;
            if (highCloseDiff.compareTo(tr) > 0) tr = highCloseDiff;
            if (lowCloseDiff.compareTo(tr) > 0) tr = lowCloseDiff;
            
            trueRanges[i - 1] = tr;
        }
        
        // Calculate ATR as simple moving average of true ranges
        BigDecimal sum = BigDecimal.ZERO;
        int count = Math.min(period, trueRanges.length);
        
        for (int i = trueRanges.length - count; i < trueRanges.length; i++) {
            sum = sum.add(trueRanges[i], mc);
        }
        
        return sum.divide(new BigDecimal(count), mc);
    }
}
