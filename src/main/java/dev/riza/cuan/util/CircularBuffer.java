package dev.riza.cuan.util;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * A thread-safe fixed-size circular buffer implementation.
 * This data structure maintains a fixed number of elements, automatically
 * removing the oldest element when a new one is added after reaching capacity.
 *
 * @param <T> The type of elements stored in the buffer
 */
public class CircularBuffer<T> implements Iterable<T> {
    private final T[] buffer;
    private int head = 0;
    private int tail = 0;
    private int size = 0;
    private final int capacity;
    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    /**
     * Creates a new circular buffer with the specified capacity.
     *
     * @param capacity The maximum number of elements the buffer can hold
     */
    @SuppressWarnings("unchecked")
    public CircularBuffer(int capacity) {
        this.capacity = capacity;
        this.buffer = (T[]) new Object[capacity];
    }

    /**
     * Adds an element to the buffer. If the buffer is full, the oldest element is overwritten.
     *
     * @param element The element to add
     */
    public void add(T element) {
        lock.writeLock().lock();
        try {
            buffer[tail] = element;
            tail = (tail + 1) % capacity;
            
            if (size < capacity) {
                size++;
            } else {
                head = (head + 1) % capacity;
            }
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Gets the element at the specified index.
     *
     * @param index The index of the element to retrieve (0 is the oldest element)
     * @return The element at the specified index
     * @throws IndexOutOfBoundsException if the index is out of range
     */
    public T get(int index) {
        lock.readLock().lock();
        try {
            if (index < 0 || index >= size) {
                throw new IndexOutOfBoundsException("Index: " + index + ", Size: " + size);
            }
            int actualIndex = (head + index) % capacity;
            return buffer[actualIndex];
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Returns the current number of elements in the buffer.
     *
     * @return The number of elements in the buffer
     */
    public int size() {
        lock.readLock().lock();
        try {
            return size;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Checks if the buffer is empty.
     *
     * @return true if the buffer is empty, false otherwise
     */
    public boolean isEmpty() {
        lock.readLock().lock();
        try {
            return size == 0;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Checks if the buffer is full.
     *
     * @return true if the buffer is full, false otherwise
     */
    public boolean isFull() {
        lock.readLock().lock();
        try {
            return size == capacity;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Clears all elements from the buffer.
     */
    public void clear() {
        lock.writeLock().lock();
        try {
            for (int i = 0; i < capacity; i++) {
                buffer[i] = null;
            }
            head = 0;
            tail = 0;
            size = 0;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Returns a list containing all elements in the buffer in order from oldest to newest.
     *
     * @return A list of all elements
     */
    public List<T> toList() {
        lock.readLock().lock();
        try {
            List<T> result = new ArrayList<>(size);
            for (int i = 0; i < size; i++) {
                result.add(buffer[(head + i) % capacity]);
            }
            return result;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Returns an iterator over the elements in this buffer in order from oldest to newest.
     *
     * @return An iterator over the elements in this buffer
     */
    @Override
    public Iterator<T> iterator() {
        // Create a snapshot of the current state to avoid concurrent modification issues
        List<T> snapshot = toList();
        return snapshot.iterator();
    }
}
