package dev.riza.cuan.aggregator;

import dev.riza.cuan.config.IndicatorRegistry;
import dev.riza.cuan.domain.model.AdaptiveBar;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.MarketConditionEvent;
import dev.riza.cuan.indicator.MarketActivityMonitor;
import org.eclipse.collections.api.list.MutableList;
import org.eclipse.collections.impl.factory.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.concurrent.locks.StampedLock;

@Component
public class AdaptiveBarAggregator{

    // State untuk bar saat ini
    private List<AggTradeEvent> currentBarTrades = new ArrayList<>();
    private Instant barOpenTime;
    private String currentSymbol;
    private BigDecimal currentOpen, currentHigh, currentLow;

    // Kondisi pasar terakhir
    private MarketConditionEvent lastMarketCondition;

    // Konfigurasi ambang batas adaptif dasar
    private final BigDecimal baseVolumeThreshold;
    private final int baseTradeCountThreshold;
    private final BigDecimal baseVolatilityMultiplierForClose; // Faktor pengali volatilitas untuk menutup bar

    private static final MathContext MC = new MathContext(10, RoundingMode.HALF_UP);

    private final ReentrantLock lock = new ReentrantLock();
    private final Sinks.Many<AdaptiveBar> sink = Sinks.many().multicast().onBackpressureBuffer();

    @Autowired
    public AdaptiveBarAggregator(@Value("${core.aggregator.baseVolumeThreshold:10}") BigDecimal baseVolumeThreshold, // BTC
                                 @Value("${core.aggregator.baseTradeCountThreshold:50}") int baseTradeCountThreshold,
                                 @Value("${core.aggregator.baseVolatilityMultiplier:0.001}") BigDecimal baseVolatilityMultiplierForClose, MarketActivityMonitor marketActivityMonitor, IndicatorRegistry indicatorRegistry) { // e.g., 0.1% price move on high vol
        this.baseVolumeThreshold = baseVolumeThreshold;
        this.baseTradeCountThreshold = baseTradeCountThreshold;
        this.baseVolatilityMultiplierForClose = baseVolatilityMultiplierForClose;
    }


    private boolean shouldCloseBar(AggTradeEvent lastTrade) {
        if (currentBarTrades.isEmpty() || lastMarketCondition == null) {
            return false; // Belum ada data atau kondisi pasar
        }

        BigDecimal currentBarVolume = currentBarTrades.stream()
                .map(AggTradeEvent::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        int currentBarTradeCount = currentBarTrades.size();

        // Logika penutupan adaptif (contoh sederhana)
        // 1. Berdasarkan Volume Adaptif
        BigDecimal dynamicVolumeThreshold = baseVolumeThreshold;
        if (lastMarketCondition.getRollingVolatility().compareTo(BigDecimal.valueOf(0.0005)) > 0) { // Jika volatilitas > 0.0005 (contoh)
            dynamicVolumeThreshold = baseVolumeThreshold.multiply(BigDecimal.valueOf(0.5)); // Kurangi threshold saat volatil
        }
        if (currentBarVolume.compareTo(dynamicVolumeThreshold) >= 0) {
            // System.out.println("Closing bar due to volume threshold: " + currentBarVolume);
            return true;
        }

        // 2. Berdasarkan Jumlah Trade Adaptif
        int dynamicTradeCountThreshold = baseTradeCountThreshold;
        if (lastMarketCondition.getTradesPerSecond() > 10) { // Jika TPS > 10 (contoh)
            dynamicTradeCountThreshold = (int) (baseTradeCountThreshold * 0.75); // Kurangi saat TPS tinggi
        }
        if (currentBarTradeCount >= dynamicTradeCountThreshold) {
            // System.out.println("Closing bar due to trade count threshold: " + currentBarTradeCount);
            return true;
        }

        // 3. Berdasarkan Pergerakan Harga x Volatilitas (dari riset)
        //    Misal: bar ditutup jika range harga bar * faktor > volatilitas_pasar_saat_ini
        //    Ini memerlukan definisi "volatilitas pasar saat ini" yang lebih jelas dari MarketConditionEvent
        if (currentHigh.compareTo(currentLow) > 0 && lastMarketCondition.getRollingVolatility().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal priceRange = currentHigh.subtract(currentLow);
            // Jika range harga signifikan relatif terhadap volatilitas pasar
            // Contoh: jika range > 2 * rollingVolatility dari MarketConditionEvent
            // (Ini memerlukan rollingVolatility dalam unit harga absolut, bukan persentase)
            return priceRange.compareTo(lastMarketCondition.getRollingVolatility().multiply(BigDecimal.valueOf(2))) >= 0;
        }


        return false; // Default jangan tutup bar
    }

    private void buildAndPublishBar(Instant closeTime, BigDecimal closePrice) {
        if (currentBarTrades.isEmpty()) return;

        BigDecimal totalVolume = BigDecimal.ZERO;
        BigDecimal totalPV = BigDecimal.ZERO; // Price * Volume
        BigDecimal buyVolume = BigDecimal.ZERO;
        BigDecimal sellVolume = BigDecimal.ZERO;

        for (AggTradeEvent t : currentBarTrades) {
            totalVolume = totalVolume.add(t.getQuantity(), MC);
            totalPV = totalPV.add(t.getPrice().multiply(t.getQuantity(), MC), MC);
            if ("buy".equalsIgnoreCase(t.getSide())) {
                buyVolume = buyVolume.add(t.getQuantity(), MC);
            } else {
                sellVolume = sellVolume.add(t.getQuantity(), MC);
            }
        }

        BigDecimal vwap = totalVolume.compareTo(BigDecimal.ZERO) > 0 ? totalPV.divide(totalVolume, MC) : closePrice;

        AdaptiveBar bar = AdaptiveBar.builder()
                .openTime(barOpenTime)
                .closeTime(closeTime)
                .symbol(currentSymbol)
                .open(currentOpen)
                .high(currentHigh)
                .low(currentLow)
                .close(closePrice)
                .vwap(vwap)
                .totalVolume(totalVolume)
                .buyVolume(buyVolume)
                .sellVolume(sellVolume)
                .tradeCount(currentBarTrades.size())
                .closingReason("ADAPTIVE_CRITERIA") // Perlu lebih detail
//                .marketConditionSnapshot(this.lastMarketCondition) // Sertakan snapshot kondisi
                .build();

        sink.tryEmitNext(bar);
    }

    public void onMarketData(Message<AggTradeEvent> message, MarketConditionEvent marketConditionEvent) {

        this.lastMarketCondition = marketConditionEvent;

        try {
            lock.lock();
            AggTradeEvent trade = message.getPayload();

            if (currentBarTrades.isEmpty()) {
                barOpenTime = trade.getTimestamp();
                currentSymbol = trade.getSymbol();
                currentOpen = trade.getPrice();
                currentHigh = trade.getPrice();
                currentLow = trade.getPrice();
            }

            currentBarTrades.add(trade);
            currentHigh = currentHigh.max(trade.getPrice());
            currentLow = currentLow.min(trade.getPrice());

            if (shouldCloseBar(trade)) {
                buildAndPublishBar(trade.getTimestamp(), trade.getPrice());
                // Reset untuk bar baru
                currentBarTrades.clear();
            }
        }finally {
            lock.unlock();
        }

    }

    public Flux<AdaptiveBar> getStream() {
        return this.sink.asFlux();
    }
}