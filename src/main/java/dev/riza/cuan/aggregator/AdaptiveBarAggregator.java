package dev.riza.cuan.aggregator;

import dev.riza.cuan.config.IndicatorRegistry;
import dev.riza.cuan.domain.model.AdaptiveBar;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.MarketConditionEvent;
import dev.riza.cuan.indicator.MarketActivityMonitor;
import org.eclipse.collections.api.list.MutableList;
import org.eclipse.collections.impl.factory.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.concurrent.locks.StampedLock;

@Component
public class AdaptiveBarAggregator{

    // Optimized state for current bar - using high-performance collections
    private final MutableList<AggTradeEvent> currentBarTrades = Lists.mutable.empty();
    private Instant barOpenTime;
    private String currentSymbol;
    private BigDecimal currentOpen, currentHigh, currentLow;

    // Incremental calculation state - avoid recalculating from scratch
    private BigDecimal currentBarVolume = BigDecimal.ZERO;
    private BigDecimal currentBuyVolume = BigDecimal.ZERO;
    private BigDecimal currentSellVolume = BigDecimal.ZERO;
    private BigDecimal currentTotalPV = BigDecimal.ZERO; // Price * Volume for VWAP
    private int currentTradeCount = 0;

    // Cached values to avoid repeated calculations
    private BigDecimal cachedDynamicVolumeThreshold;
    private int cachedDynamicTradeCountThreshold;
    private long lastMarketConditionUpdateTime = 0;

    // Market condition state
    private MarketConditionEvent lastMarketCondition;

    // Configuration thresholds
    private final BigDecimal baseVolumeThreshold;
    private final int baseTradeCountThreshold;
    private final BigDecimal baseVolatilityMultiplierForClose;

    // Constants for performance
    private static final MathContext MC = new MathContext(10, RoundingMode.HALF_UP);
    private static final BigDecimal ZERO = BigDecimal.ZERO;
    private static final BigDecimal VOLATILITY_THRESHOLD = new BigDecimal("0.0005");
    private static final BigDecimal VOLUME_MULTIPLIER = new BigDecimal("0.5");
    private static final BigDecimal TRADE_COUNT_MULTIPLIER = new BigDecimal("0.75");
    private static final BigDecimal PRICE_RANGE_MULTIPLIER = new BigDecimal("2");
    private static final double TPS_THRESHOLD = 10.0;

    // Optimized locking - StampedLock is more efficient than ReentrantLock for read-heavy workloads
    private final StampedLock lock = new StampedLock();
    private final Sinks.Many<AdaptiveBar> sink = Sinks.many().multicast().onBackpressureBuffer();

    @Autowired
    public AdaptiveBarAggregator(@Value("${core.aggregator.baseVolumeThreshold:10}") BigDecimal baseVolumeThreshold, // BTC
                                 @Value("${core.aggregator.baseTradeCountThreshold:50}") int baseTradeCountThreshold,
                                 @Value("${core.aggregator.baseVolatilityMultiplier:0.001}") BigDecimal baseVolatilityMultiplierForClose) { // e.g., 0.1% price move on high vol
        this.baseVolumeThreshold = baseVolumeThreshold;
        this.baseTradeCountThreshold = baseTradeCountThreshold;
        this.baseVolatilityMultiplierForClose = baseVolatilityMultiplierForClose;

        // Initialize cached thresholds
        this.cachedDynamicVolumeThreshold = baseVolumeThreshold;
        this.cachedDynamicTradeCountThreshold = baseTradeCountThreshold;
    }


    // Add field to track closing reason
    private String lastClosingReason = "UNKNOWN";

    /**
     * Optimized shouldCloseBar method with caching and incremental calculations
     * Now tracks the specific reason for bar closure
     */
    private boolean shouldCloseBar(AggTradeEvent lastTrade) {
        if (currentTradeCount == 0 || lastMarketCondition == null) {
            return false; // No data or market condition available
        }

        // Update cached thresholds only when market condition changes
        updateCachedThresholdsIfNeeded();

        // 1. Volume-based adaptive closing (using cached incremental volume)
        if (currentBarVolume.compareTo(cachedDynamicVolumeThreshold) >= 0) {
            lastClosingReason = buildVolumeClosingReason();
            return true;
        }

        // 2. Trade count-based adaptive closing (using cached count)
        if (currentTradeCount >= cachedDynamicTradeCountThreshold) {
            lastClosingReason = buildTradeCountClosingReason();
            return true;
        }

        // 3. Price range-based closing (optimized BigDecimal operations)
        if (currentHigh.compareTo(currentLow) > 0 &&
            lastMarketCondition.getRollingVolatility().compareTo(ZERO) > 0) {
            BigDecimal priceRange = currentHigh.subtract(currentLow);
            BigDecimal volatilityThreshold = lastMarketCondition.getRollingVolatility()
                    .multiply(PRICE_RANGE_MULTIPLIER, MC);
            if (priceRange.compareTo(volatilityThreshold) >= 0) {
                lastClosingReason = buildPriceRangeClosingReason(priceRange, volatilityThreshold);
                return true;
            }
        }

        return false; // Default: don't close bar
    }

    /**
     * Build detailed closing reason for volume-based closure
     */
    private String buildVolumeClosingReason() {
        boolean isAdaptive = cachedDynamicVolumeThreshold.compareTo(baseVolumeThreshold) != 0;
        if (isAdaptive) {
            return String.format("VOLUME_THRESHOLD_ADAPTIVE(%.4f/%.4f,volatility=%.6f)",
                currentBarVolume.doubleValue(),
                cachedDynamicVolumeThreshold.doubleValue(),
                lastMarketCondition.getRollingVolatility().doubleValue());
        } else {
            return String.format("VOLUME_THRESHOLD_BASE(%.4f/%.4f)",
                currentBarVolume.doubleValue(),
                cachedDynamicVolumeThreshold.doubleValue());
        }
    }

    /**
     * Build detailed closing reason for trade count-based closure
     */
    private String buildTradeCountClosingReason() {
        boolean isAdaptive = cachedDynamicTradeCountThreshold != baseTradeCountThreshold;
        if (isAdaptive) {
            return String.format("TRADE_COUNT_ADAPTIVE(%d/%d,tps=%.1f)",
                currentTradeCount,
                cachedDynamicTradeCountThreshold,
                lastMarketCondition.getTradesPerSecond());
        } else {
            return String.format("TRADE_COUNT_BASE(%d/%d)",
                currentTradeCount,
                cachedDynamicTradeCountThreshold);
        }
    }

    /**
     * Build detailed closing reason for price range-based closure
     */
    private String buildPriceRangeClosingReason(BigDecimal priceRange, BigDecimal volatilityThreshold) {
        return String.format("PRICE_RANGE_VOLATILITY(range=%.4f,threshold=%.4f,volatility=%.6f)",
            priceRange.doubleValue(),
            volatilityThreshold.doubleValue(),
            lastMarketCondition.getRollingVolatility().doubleValue());
    }

    /**
     * Update cached thresholds only when market condition changes to avoid repeated calculations
     */
    private void updateCachedThresholdsIfNeeded() {
        long currentTime = lastMarketCondition.getTimestamp().toEpochMilli();
        if (currentTime != lastMarketConditionUpdateTime) {
            lastMarketConditionUpdateTime = currentTime;

            // Cache dynamic volume threshold
            if (lastMarketCondition.getRollingVolatility().compareTo(VOLATILITY_THRESHOLD) > 0) {
                cachedDynamicVolumeThreshold = baseVolumeThreshold.multiply(VOLUME_MULTIPLIER, MC);
            } else {
                cachedDynamicVolumeThreshold = baseVolumeThreshold;
            }

            // Cache dynamic trade count threshold
            if (lastMarketCondition.getTradesPerSecond() > TPS_THRESHOLD) {
                cachedDynamicTradeCountThreshold = (int) (baseTradeCountThreshold * TRADE_COUNT_MULTIPLIER.doubleValue());
            } else {
                cachedDynamicTradeCountThreshold = baseTradeCountThreshold;
            }
        }
    }

    /**
     * Optimized buildAndPublishBar using pre-calculated incremental values
     */
    private void buildAndPublishBar(Instant closeTime, BigDecimal closePrice) {
        if (currentTradeCount == 0) return;

        // Use pre-calculated incremental values instead of recalculating
        BigDecimal vwap = currentBarVolume.compareTo(ZERO) > 0 ?
                currentTotalPV.divide(currentBarVolume, MC) : closePrice;

        AdaptiveBar bar = AdaptiveBar.builder()
                .openTime(barOpenTime)
                .closeTime(closeTime)
                .symbol(currentSymbol)
                .open(currentOpen)
                .high(currentHigh)
                .low(currentLow)
                .close(closePrice)
                .vwap(vwap)
                .totalVolume(currentBarVolume)
                .buyVolume(currentBuyVolume)
                .sellVolume(currentSellVolume)
                .tradeCount(currentTradeCount)
                .closingReason(lastClosingReason) // Now uses the specific closing reason
                .marketConditionSnapshot(this.lastMarketCondition)
                .build();

        sink.tryEmitNext(bar);
    }

    /**
     * Optimized onMarketData with StampedLock and incremental calculations
     */
    public void onMarketData(Message<AggTradeEvent> message, MarketConditionEvent marketConditionEvent) {
        this.lastMarketCondition = marketConditionEvent;

        long stamp = lock.writeLock();
        try {
            AggTradeEvent trade = message.getPayload();

            // Initialize bar state if this is the first trade
            if (currentTradeCount == 0) {
                initializeNewBar(trade);
            }

            // Add trade and update incremental calculations
            addTradeToCurrentBar(trade);

            // Check if bar should be closed
            if (shouldCloseBar(trade)) {
                buildAndPublishBar(trade.getTimestamp(), trade.getPrice());
                resetBarState();
            }
        } finally {
            lock.unlockWrite(stamp);
        }
    }

    /**
     * Initialize a new bar with the first trade
     */
    private void initializeNewBar(AggTradeEvent trade) {
        barOpenTime = trade.getTimestamp();
        currentSymbol = trade.getSymbol();
        currentOpen = trade.getPrice();
        currentHigh = trade.getPrice();
        currentLow = trade.getPrice();

        // Reset incremental calculations
        currentBarVolume = ZERO;
        currentBuyVolume = ZERO;
        currentSellVolume = ZERO;
        currentTotalPV = ZERO;
        currentTradeCount = 0;
    }

    /**
     * Add trade to current bar with optimized incremental calculations
     */
    private void addTradeToCurrentBar(AggTradeEvent trade) {
        // Add to trade list (using high-performance collection)
        currentBarTrades.add(trade);

        // Update price range
        currentHigh = currentHigh.max(trade.getPrice());
        currentLow = currentLow.min(trade.getPrice());

        // Incremental volume calculations
        BigDecimal quantity = trade.getQuantity();
        currentBarVolume = currentBarVolume.add(quantity, MC);
        currentTotalPV = currentTotalPV.add(trade.getPrice().multiply(quantity, MC), MC);
        currentTradeCount++;

        // Optimized side comparison - avoid string operations
        if (isBuyTrade(trade.getSide())) {
            currentBuyVolume = currentBuyVolume.add(quantity, MC);
        } else {
            currentSellVolume = currentSellVolume.add(quantity, MC);
        }
    }

    /**
     * Optimized trade side detection to avoid repeated string comparisons
     */
    private boolean isBuyTrade(String side) {
        // Use charAt for faster comparison than equalsIgnoreCase
        return side != null && side.length() > 0 &&
               (side.charAt(0) == 'b' || side.charAt(0) == 'B');
    }

    /**
     * Reset bar state for new bar
     */
    private void resetBarState() {
        currentBarTrades.clear();
        currentBarVolume = ZERO;
        currentBuyVolume = ZERO;
        currentSellVolume = ZERO;
        currentTotalPV = ZERO;
        currentTradeCount = 0;
        lastClosingReason = "UNKNOWN"; // Reset closing reason for new bar
    }

    public Flux<AdaptiveBar> getStream() {
        return this.sink.asFlux();
    }
}