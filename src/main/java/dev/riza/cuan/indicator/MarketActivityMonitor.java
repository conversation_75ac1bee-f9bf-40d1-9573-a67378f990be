package dev.riza.cuan.indicator;



import dev.riza.cuan.config.MarketDataGateway;
import dev.riza.cuan.config.MarketDataIndicator;
import dev.riza.cuan.domain.model.MarketConditionEvent;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.util.CircularBuffer;
import dev.riza.cuan.util.StatisticalUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

@Component
public class MarketActivityMonitor implements MarketDataIndicator<AggTradeEvent, MarketConditionEvent> {

    private final CircularBuffer<BigDecimal> recentPrices;
    private final CircularBuffer<AggTradeEvent> recentTradesForVolumeRate;
    private final CircularBuffer<Instant> tradeTimestamps;

    private final int volatilityWindowSize;
    private final int volumeRateWindowSize;
    private final Duration tpsWindowDuration;

    private static final MathContext MC = new MathContext(10, RoundingMode.HALF_UP);
    private int tradeCount = 0;
    private final int publishConditionEventEveryNTrades;

    private final ReentrantLock lock = new ReentrantLock();
    private final Sinks.Many<MarketConditionEvent> sink = Sinks.many().multicast().onBackpressureBuffer();
    private final SubscribableChannel inputChannel = new DirectChannel();

    @Autowired
    public MarketActivityMonitor(@Value("${core.analyzer.volatilityWindowSize:200}") int volatilityWindowSize,
                                 @Value("${core.analyzer.volumeRateWindowSize:50}") int volumeRateWindowSize,
                                 @Value("${core.analyzer.tpsWindowSeconds:10}") int tpsWindowSeconds,
                                 @Value("${core.analyzer.publishEventEvery:5}") int publishConditionEventEveryNTrades) {
        this.volatilityWindowSize = volatilityWindowSize;
        this.volumeRateWindowSize = volumeRateWindowSize;
        this.tpsWindowDuration = Duration.ofSeconds(tpsWindowSeconds);
        this.publishConditionEventEveryNTrades = publishConditionEventEveryNTrades;

        this.recentPrices = new CircularBuffer<>(volatilityWindowSize);
        this.recentTradesForVolumeRate = new CircularBuffer<>(volumeRateWindowSize);
        this.tradeTimestamps = new CircularBuffer<>(200); // Cukup besar untuk menampung trade dalam tpsWindowDuration

        inputChannel.subscribe(message -> {
            // Safe cast since we know this component only handles AggTradeEvent messages
            @SuppressWarnings("unchecked")
            Message<AggTradeEvent> aggTradeMessage = (Message<AggTradeEvent>) message;
            onMarketData(aggTradeMessage);
        });

    }

    private void cleanupOldTimestamps(Instant now) {
        Instant cutoff = now.minus(tpsWindowDuration);
        List<Instant> currentTimestamps = tradeTimestamps.toList();
        tradeTimestamps.clear(); // Hati-hati, ini akan menghapus semua jika tidak dilakukan dengan benar
        for (Instant ts : currentTimestamps) {
            if (!ts.isBefore(cutoff)) {
                tradeTimestamps.add(ts); // Tambahkan kembali yang masih valid
            }
        }
    }

    private void publishMarketCondition(Instant timestamp) {
        // Calculate volatility with proper window size validation
        BigDecimal volatility = calculateVolatility();

        // Calculate TPS using time-based window
        double tps = calculateTradesPerSecond();

        // Calculate volume rate properly normalized by window size
        BigDecimal volumeRate = calculateVolumeRate();

        if (!recentTradesForVolumeRate.isEmpty()) {
            for (AggTradeEvent t : recentTradesForVolumeRate.toList()) {
                volumeRate = volumeRate.add(t.getQuantity());
            }
            // Bisa dinormalisasi per detik jika diinginkan
        }

        MarketConditionEvent conditionEvent = MarketConditionEvent.builder()
                .timestamp(timestamp)
                .rollingVolatility(volatility)
                .tradesPerSecond(tps)
                .volumeRate(volumeRate)
                .build();

        sink.tryEmitNext(conditionEvent);
    }

    /**
     * Calculate volatility using the configured volatility window size
     */
    private BigDecimal calculateVolatility() {
        if (recentPrices.size() < 2) {
            return BigDecimal.ZERO;
        }

        // Use the actual window size for validation and calculation
        List<BigDecimal> prices = recentPrices.toList();
        if (prices.size() < Math.min(volatilityWindowSize / 2, 2)) {
            // Not enough data points for reliable volatility calculation
            return BigDecimal.ZERO;
        }

        return StatisticalUtils.standardDeviation(prices, MC);
    }

    /**
     * Calculate trades per second using the configured TPS window
     */
    private double calculateTradesPerSecond() {
        if (tradeTimestamps.size() <= 1) {
            return 0.0;
        }

        List<Instant> currentTimestamps = tradeTimestamps.toList();
        Duration duration = Duration.between(
            currentTimestamps.get(0),
            currentTimestamps.get(currentTimestamps.size() - 1)
        );

        if (duration.isZero()) {
            // All trades in same instant - return count as TPS
            return currentTimestamps.size();
        }

        return (double) currentTimestamps.size() / duration.toSeconds();
    }

    /**
     * Calculate volume rate properly normalized by the configured volume rate window size
     */
    private BigDecimal calculateVolumeRate() {
        if (recentTradesForVolumeRate.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // Sum all volumes in the window
        BigDecimal totalVolume = BigDecimal.ZERO;
        List<AggTradeEvent> trades = recentTradesForVolumeRate.toList();

        for (AggTradeEvent trade : trades) {
            totalVolume = totalVolume.add(trade.getQuantity(), MC);
        }

        // IMPORTANT: Normalize by the actual window size being used
        // This gives us "average volume per trade" in the window
        int actualTradeCount = trades.size();
        if (actualTradeCount > 0) {
            // Volume rate = total volume / number of trades in window
            // This represents the average trade size in the current window
            return totalVolume.divide(BigDecimal.valueOf(actualTradeCount), MC);
        }

        return BigDecimal.ZERO;
    }

    @Override
    public void onMarketData(Message<AggTradeEvent> message) {
        lock.lock();
        try {
            AggTradeEvent trade = message.getPayload();
            Instant now = trade.getTimestamp(); // Gunakan timestamp trade

            // Update untuk Volatilitas
            recentPrices.add(trade.getPrice());

            // Update untuk Volume Rate
            recentTradesForVolumeRate.add(trade);

            // Update untuk TPS
            tradeTimestamps.add(now);
            cleanupOldTimestamps(now);

            tradeCount++;
            if (tradeCount >= publishConditionEventEveryNTrades) {
                tradeCount = 0;
                publishMarketCondition(now);
            }
        } finally {
            lock.unlock();
        }


    }

    @Override
    public MessageChannel getInputChannel() {
        return this.inputChannel;
    }

    @Override
    public Duration getTimeframe() {
        return null;
    }

    @Override
    public Flux<MarketConditionEvent> getStream() {
        return sink.asFlux();
    }
}
