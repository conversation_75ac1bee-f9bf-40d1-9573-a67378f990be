spring.application.name=cuan01

# Server Configuration
server.port=8080

# Database Configuration (TimescaleDB/PostgreSQL)
#spring.datasource.url=*******************************************
#spring.datasource.username=postgres
#spring.datasource.password=postgres
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
#spring.jpa.hibernate.ddl-auto=update
#spring.jpa.show-sql=false

# VWAP Indicator Configuration
indicator.vwap.windowSize=100
indicator.vwap.timeframe=PT2S

# Adaptive VWAP Configuration
indicator.vwap.adaptive.initialWindowSize=100
indicator.vwap.adaptive.minWindowSize=20
indicator.vwap.adaptive.maxWindowSize=500
indicator.vwap.adaptive.volatilitySensitivity=1.0
indicator.vwap.adaptive.volumeSensitivity=1.0
indicator.vwap.adaptive.adjustEvery=20

# Simulation Configuration
simulation.account.startingBalance=10000.00

# Adaptive Market Strategy Configuration
strategy.adaptive.volatilityBaselineWindow=50
strategy.adaptive.momentumWindow=20
strategy.adaptive.trailingStopBase=30
strategy.adaptive.trailingStopVolatilityFactor=2.0
strategy.adaptive.volatilityThresholdMultiplier=1.5

