# MarketActivityMonitor Configuration Guide

## Overview

The MarketActivityMonitor uses sliding window calculations to analyze market conditions in real-time. The window sizes control how much historical data is used for each calculation, directly affecting the sensitivity and smoothness of market condition detection.

## Configuration Parameters

### 1. volatilityWindowSize (default: 20)

**Purpose**: Controls how many recent price points are used to calculate rolling volatility.

**Configuration**: `core.analyzer.volatilityWindowSize=20`

**How it works**:
```java
// Creates a circular buffer that holds the last N prices
this.recentPrices = new CircularBuffer<>(volatilityWindowSize);

// Volatility calculation uses all prices in the buffer
BigDecimal volatility = StatisticalUtils.standardDeviation(recentPrices.toList(), MC);
```

**Impact on Trading**:
- **Smaller values (5-10)**: 
  - ✅ Very responsive to price changes
  - ✅ Quick detection of volatility spikes
  - ❌ More noise, false signals
  - ❌ Frequent adaptive threshold changes

- **Medium values (15-30)**: 
  - ✅ Balanced responsiveness and stability
  - ✅ Good for most trading scenarios
  - ✅ Reduces noise while maintaining sensitivity

- **Larger values (50-100)**:
  - ✅ Very smooth volatility calculation
  - ✅ Fewer false signals
  - ❌ Slower to detect volatility changes
  - ❌ May miss short-term opportunities

### 2. volumeRateWindowSize (default: 50)

**Purpose**: Controls how many recent trades are used to calculate volume rate.

**Configuration**: `core.analyzer.volumeRateWindowSize=50`

**How it works**:
```java
// Creates a circular buffer that holds the last N trades
this.recentTradesForVolumeRate = new CircularBuffer<>(volumeRateWindowSize);

// Volume rate calculation sums all trade quantities in the buffer
BigDecimal volumeRate = BigDecimal.ZERO;
for (AggTradeEvent t : recentTradesForVolumeRate.toList()) {
    volumeRate = volumeRate.add(t.getQuantity());
}
```

**Impact on Trading**:
- **Smaller values (10-25)**:
  - ✅ Quick response to volume changes
  - ✅ Detects sudden volume spikes immediately
  - ❌ More volatile volume rate readings
  - ❌ May trigger false volume-based signals

- **Medium values (30-75)**:
  - ✅ Balanced volume rate calculation
  - ✅ Good representation of recent trading activity
  - ✅ Suitable for most market conditions

- **Larger values (100-200)**:
  - ✅ Very smooth volume rate
  - ✅ Represents longer-term volume trends
  - ❌ Slower to detect volume changes
  - ❌ May miss short-term volume opportunities

### 3. tpsWindowSeconds (default: 10)

**Purpose**: Time window for calculating trades per second (TPS).

**Configuration**: `core.analyzer.tpsWindowSeconds=10`

**How it works**:
```java
// Keeps timestamps of trades within the last N seconds
Duration tpsWindowDuration = Duration.ofSeconds(tpsWindowSeconds);

// Calculates TPS based on trades in the time window
double tps = (double) currentTimestamps.size() / duration.toSeconds();
```

### 4. publishEventEvery (default: 5)

**Purpose**: How often to publish market condition events (every N trades).

**Configuration**: `core.analyzer.publishEventEvery=5`

## Real-World Configuration Examples

### High-Frequency Trading Setup
```properties
# Very responsive to market changes
core.analyzer.volatilityWindowSize=10    # Quick volatility detection
core.analyzer.volumeRateWindowSize=25    # Responsive volume tracking
core.analyzer.tpsWindowSeconds=5         # Short TPS window
core.analyzer.publishEventEvery=3        # Frequent updates
```

**Use Case**: Scalping, market making, arbitrage
**Benefits**: Immediate response to market changes
**Risks**: More noise, frequent false signals

### Medium-Frequency Trading Setup
```properties
# Balanced approach (default values)
core.analyzer.volatilityWindowSize=20    # Balanced volatility
core.analyzer.volumeRateWindowSize=50    # Stable volume rate
core.analyzer.tpsWindowSeconds=10        # Standard TPS window
core.analyzer.publishEventEvery=5        # Regular updates
```

**Use Case**: Swing trading, momentum strategies
**Benefits**: Good balance of responsiveness and stability
**Risks**: Minimal, well-tested configuration

### Conservative Trading Setup
```properties
# Smooth, stable market condition detection
core.analyzer.volatilityWindowSize=50    # Smooth volatility
core.analyzer.volumeRateWindowSize=100   # Long-term volume trends
core.analyzer.tpsWindowSeconds=30        # Longer TPS window
core.analyzer.publishEventEvery=10       # Less frequent updates
```

**Use Case**: Position trading, trend following
**Benefits**: Fewer false signals, stable readings
**Risks**: Slower response to market changes

## Impact on AdaptiveBarAggregator

The window sizes directly affect how the AdaptiveBarAggregator behaves:

### Volatility Window Impact
```java
// In AdaptiveBarAggregator.shouldCloseBar()
if (lastMarketCondition.getRollingVolatility().compareTo(VOLATILITY_THRESHOLD) > 0) {
    // Smaller volatility window = more frequent adaptive behavior
    cachedDynamicVolumeThreshold = baseVolumeThreshold.multiply(VOLUME_MULTIPLIER);
}
```

### Volume Rate Window Impact
```java
// Volume rate affects market condition assessment
// Larger window = smoother volume rate = more stable thresholds
MarketConditionEvent.volumeRate // Used for market analysis
```

## Monitoring and Tuning

### Key Metrics to Monitor

1. **Adaptive Closure Frequency**:
   ```java
   // Count bars closed due to adaptive criteria
   long adaptiveBars = bars.stream()
       .filter(bar -> bar.getClosingReason().contains("ADAPTIVE"))
       .count();
   ```

2. **Volatility Responsiveness**:
   ```java
   // Monitor how quickly volatility changes are detected
   double volatilityChangeRate = calculateVolatilityChangeRate();
   ```

3. **Volume Rate Stability**:
   ```java
   // Check volume rate smoothness
   double volumeRateVariance = calculateVolumeRateVariance();
   ```

### Tuning Guidelines

#### If you see too many adaptive bar closures:
- **Increase** `volatilityWindowSize` (e.g., 20 → 30)
- **Increase** `volumeRateWindowSize` (e.g., 50 → 75)

#### If you're missing market opportunities:
- **Decrease** `volatilityWindowSize` (e.g., 20 → 15)
- **Decrease** `volumeRateWindowSize` (e.g., 50 → 30)
- **Decrease** `publishEventEvery` (e.g., 5 → 3)

#### If market condition updates are too frequent:
- **Increase** `publishEventEvery` (e.g., 5 → 8)
- **Increase** `tpsWindowSeconds` (e.g., 10 → 15)

## Performance Considerations

### Memory Usage
```java
// Total memory per monitor instance
int totalMemory = volatilityWindowSize * sizeof(BigDecimal) +
                  volumeRateWindowSize * sizeof(AggTradeEvent) +
                  200 * sizeof(Instant); // TPS timestamps
```

### CPU Usage
- Larger windows = more data to process per calculation
- More frequent publishing = higher CPU usage
- Consider trade-offs based on your hardware

## Best Practices

1. **Start with defaults** and monitor performance
2. **Adjust gradually** - change one parameter at a time
3. **Backtest changes** before deploying to production
4. **Monitor adaptive behavior** frequency
5. **Document configuration changes** and their impact

## Example Monitoring Code

```java
@Component
public class MarketConditionMonitor {
    
    @EventListener
    public void onMarketCondition(MarketConditionEvent event) {
        // Log volatility patterns
        if (event.getRollingVolatility().compareTo(new BigDecimal("0.001")) > 0) {
            log.info("High volatility detected: {}", event.getRollingVolatility());
        }
        
        // Monitor TPS patterns
        if (event.getTradesPerSecond() > 15.0) {
            log.info("High frequency trading detected: {} TPS", event.getTradesPerSecond());
        }
        
        // Track volume rate trends
        log.debug("Volume rate: {}, Volatility: {}, TPS: {}", 
            event.getVolumeRate(), 
            event.getRollingVolatility(), 
            event.getTradesPerSecond());
    }
}
```

This configuration system provides fine-grained control over market condition detection, enabling optimization for different trading strategies and market conditions.
